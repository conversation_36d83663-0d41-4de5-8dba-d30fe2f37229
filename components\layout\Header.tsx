"use client";

import React, { useState, useEffect, useRef } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import Button from "@/components/ui/Button";
import ThemeToggleWrapper from "@/components/ui/ThemeToggleWrapper";

const primaryNavigationItems = [
  { name: "Home", href: "/" },
  { name: "Forums", href: "/forums" },
  { name: "EV Listings", href: "/ev-listings" },
  { name: "Marketplace", href: "/marketplace" },
];

const secondaryNavigationItems = [
  { name: "Member Garage", href: "/garage" },
  { name: "Business Directory", href: "/directory" },
  { name: "What's New", href: "/whats-new" },
];

const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const pathname = usePathname();

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <header className="bg-glass-bg backdrop-blur-lg border-b border-border sticky top-0 z-50 shadow-sm transition-all duration-300">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-20">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-3 group">
              <div className="w-10 h-10 bg-gradient-primary rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-105">
                <span className="text-white font-bold text-xl">⚡</span>
              </div>
              <div className="flex flex-col">
                <span className="text-xl font-bold text-foreground">
                  EV Community
                </span>
                <span className="text-xs text-foreground-muted -mt-1">
                  Electric Future
                </span>
              </div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-2 ml-3">
            {primaryNavigationItems.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={cn(
                  "px-4 py-2.5 rounded-xl text-sm font-semibold transition-all duration-200 relative group",
                  pathname === item.href
                    ? "bg-gradient-primary text-white shadow-lg"
                    : "text-foreground-muted hover:text-foreground hover:bg-background-secondary"
                )}
              >
                {item.name}
                {pathname === item.href && (
                  <div className="absolute inset-0 bg-gradient-primary rounded-xl opacity-20 animate-pulse"></div>
                )}
              </Link>
            ))}

            {/* Community Dropdown */}
            <div className="relative" ref={dropdownRef}>
              <button
                onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                className={cn(
                  "px-4 py-2.5 rounded-xl text-sm font-semibold transition-all duration-200 relative group flex items-center",
                  secondaryNavigationItems.some(
                    (item) => pathname === item.href
                  )
                    ? "bg-gradient-primary text-white shadow-lg"
                    : "text-foreground-muted hover:text-foreground hover:bg-background-secondary"
                )}
              >
                Community
                <svg
                  className={cn(
                    "ml-1 h-4 w-4 transition-transform duration-200",
                    isDropdownOpen && "rotate-180"
                  )}
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>

              {isDropdownOpen && (
                <div className="absolute top-full left-0 mt-2 w-48 bg-dropdown-bg backdrop-blur-lg border border-border rounded-xl shadow-xl z-50 py-2">
                  {secondaryNavigationItems.map((item) => (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={cn(
                        "block px-4 py-2 text-sm font-medium transition-colors",
                        pathname === item.href
                          ? "bg-primary text-white"
                          : "text-foreground-muted hover:text-foreground hover:bg-background-secondary"
                      )}
                      onClick={() => setIsDropdownOpen(false)}
                    >
                      {item.name}
                    </Link>
                  ))}
                </div>
              )}
            </div>
          </nav>

          {/* Search Bar */}
          <div className="hidden md:flex flex-1 max-w-lg mx-8">
            <div className="relative w-full group">
              <input
                type="text"
                placeholder="Search everything..."
                className="w-full px-5 py-3 pl-12 pr-16 text-sm bg-background-secondary border border-border rounded-2xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 group-hover:shadow-md focus:shadow-lg text-foreground placeholder:text-foreground-muted"
              />
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <span className="text-foreground-muted text-lg">🔍</span>
              </div>
              <div className="absolute inset-y-0 right-0 pr-4 flex items-center">
                <kbd className="px-2 py-1 text-xs font-semibold text-foreground-muted bg-background border border-border rounded-md">
                  ⌘K
                </kbd>
              </div>
            </div>
          </div>

          {/* User Actions */}
          <div className="flex items-center space-x-3">
            <ThemeToggleWrapper />
            <Button
              variant="outline"
              size="sm"
              className="hidden md:inline-flex"
            >
              Sign In
            </Button>
            <Button size="sm" className="hidden md:inline-flex shadow-lg">
              Join Community
            </Button>

            {/* Mobile menu button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden p-2 rounded-md text-text-secondary hover:text-foreground hover:bg-secondary-light"
            >
              <span className="sr-only">Open main menu</span>
              {isMenuOpen ? (
                <span className="text-xl">✕</span>
              ) : (
                <span className="text-xl">☰</span>
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 border-t border-border-light">
              {/* Mobile Search */}
              <div className="mb-3">
                <input
                  type="text"
                  placeholder="Search..."
                  className="w-full px-3 py-2 text-sm border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>

              {/* Mobile Navigation Items */}
              {primaryNavigationItems.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className={cn(
                    "block px-3 py-2 rounded-md text-base font-medium",
                    pathname === item.href
                      ? "bg-primary text-white"
                      : "text-foreground-muted hover:text-foreground hover:bg-background-secondary"
                  )}
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}

              {/* Mobile Secondary Navigation */}
              <div className="pt-2 border-t border-border-light">
                <div className="px-3 py-2 text-xs font-semibold text-foreground-muted uppercase tracking-wider">
                  Community
                </div>
                {secondaryNavigationItems.map((item) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={cn(
                      "block px-3 py-2 rounded-md text-base font-medium",
                      pathname === item.href
                        ? "bg-primary text-white"
                        : "text-foreground-muted hover:text-foreground hover:bg-background-secondary"
                    )}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {item.name}
                  </Link>
                ))}
              </div>

              {/* Mobile User Actions */}
              <div className="pt-4 border-t border-border-light space-y-2">
                <Button variant="outline" size="sm" className="w-full">
                  Sign In
                </Button>
                <Button size="sm" className="w-full">
                  Join Community
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
