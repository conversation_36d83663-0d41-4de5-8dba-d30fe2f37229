import React from "react";
import Link from "next/link";
import { forumCategories, getRecentThreads } from "@/lib/data/forums";
import { formatTimeAgo } from "@/lib/utils";
import Card from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import ForumThreadCard from "@/components/cards/ForumThreadCard";

export default function ForumsPage() {
  const recentThreads = getRecentThreads(10);

  return (
    <div className="min-h-screen bg-secondary-light">
      {/* Page Header */}
      <div className="border-b border-border-light">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <h1 className="text-3xl font-bold text-foreground">
                Community Forums
              </h1>
              <p className="text-text-secondary mt-1">
                Connect with fellow EV enthusiasts and share your experiences
              </p>
            </div>
            <div className="mt-4 md:mt-0">
              <Button>Start New Discussion</Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* Search Bar */}
            <Card className="mb-6">
              <div className="flex gap-4">
                <Input
                  placeholder="Search discussions..."
                  leftIcon={<span>🔍</span>}
                  className="flex-1"
                />
                <Button variant="outline">Search</Button>
              </div>
            </Card>

            {/* Forum Categories */}
            <Card className="mb-8">
              <Card.Header>
                <Card.Title>Forum Categories</Card.Title>
              </Card.Header>
              <Card.Content>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {forumCategories.map((category) => (
                    <Link key={category.id} href={`/forums/${category.id}`}>
                      <div className="p-4 border border-border-light rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                        <div className="flex items-start space-x-3">
                          <div className="text-2xl">{category.icon}</div>
                          <div className="flex-1 min-w-0">
                            <h3 className="font-semibold text-foreground">
                              {category.name}
                            </h3>
                            <p className="text-sm text-text-secondary line-clamp-2 mb-2">
                              {category.description}
                            </p>
                            <div className="flex items-center space-x-4 text-sm text-text-secondary">
                              <span>{category.threadCount} threads</span>
                              <span>{category.postCount} posts</span>
                            </div>
                            {category.lastActivity && (
                              <div className="mt-2 text-xs text-text-secondary">
                                Last:{" "}
                                <span className="font-medium">
                                  {category.lastActivity.threadTitle}
                                </span>
                                <span className="mx-1">by</span>
                                <span className="font-medium">
                                  {category.lastActivity.userName}
                                </span>
                                <span className="mx-1">•</span>
                                {formatTimeAgo(category.lastActivity.timestamp)}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              </Card.Content>
            </Card>

            {/* Recent Discussions */}
            <Card>
              <Card.Header>
                <div className="flex items-center justify-between">
                  <Card.Title>Recent Discussions</Card.Title>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      Latest
                    </Button>
                    <Button variant="ghost" size="sm">
                      Popular
                    </Button>
                    <Button variant="ghost" size="sm">
                      Trending
                    </Button>
                  </div>
                </div>
              </Card.Header>
              <Card.Content>
                <div className="space-y-4">
                  {recentThreads.map((thread) => (
                    <ForumThreadCard
                      key={thread.id}
                      thread={thread}
                      compact={true}
                    />
                  ))}
                </div>
              </Card.Content>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Forum Stats */}
            <Card>
              <Card.Header>
                <Card.Title>Forum Statistics</Card.Title>
              </Card.Header>
              <Card.Content>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-text-secondary">Total Threads</span>
                    <span className="font-semibold">3,456</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-text-secondary">Total Posts</span>
                    <span className="font-semibold">28,934</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-text-secondary">Active Members</span>
                    <span className="font-semibold">1,247</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-text-secondary">Online Now</span>
                    <span className="font-semibold text-accent-green">89</span>
                  </div>
                </div>
              </Card.Content>
            </Card>

            {/* Popular Tags */}
            <Card>
              <Card.Header>
                <Card.Title>Popular Tags</Card.Title>
              </Card.Header>
              <Card.Content>
                <div className="flex flex-wrap gap-2">
                  {[
                    "Tesla",
                    "Charging",
                    "Road Trip",
                    "FSD",
                    "Battery",
                    "Winter",
                    "Maintenance",
                    "Supercharger",
                  ].map((tag) => (
                    <span
                      key={tag}
                      className="px-2 py-1 bg-secondary-light text-text-secondary text-sm rounded-md hover:bg-primary hover:text-white cursor-pointer transition-colors"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </Card.Content>
            </Card>

            {/* Forum Guidelines */}
            <Card>
              <Card.Header>
                <Card.Title>Forum Guidelines</Card.Title>
              </Card.Header>
              <Card.Content>
                <ul className="space-y-2 text-sm text-text-secondary">
                  <li>• Be respectful and constructive</li>
                  <li>• Search before posting</li>
                  <li>• Use descriptive titles</li>
                  <li>• Stay on topic</li>
                  <li>• No spam or self-promotion</li>
                </ul>
                <Button variant="outline" size="sm" className="w-full mt-3">
                  Read Full Guidelines
                </Button>
              </Card.Content>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
