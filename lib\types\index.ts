// EV Community Platform - Type Definitions

export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: 'community' | 'business' | 'moderator';
  joinDate: string;
  location?: string;
  bio?: string;
  garage: Vehicle[];
  forumActivity: {
    posts: number;
    threads: number;
    reputation: number;
  };
}

export interface Vehicle {
  id: string;
  userId: string;
  brand: string;
  model: string;
  year: number;
  trim?: string;
  color: string;
  photos: string[];
  specifications: VehicleSpecs;
  modifications?: string[];
  description?: string;
  isForSale?: boolean;
  price?: number;
  mileage?: number;
  condition?: 'new' | 'excellent' | 'good' | 'fair';
  location?: string;
  contactInfo?: string;
}

export interface VehicleSpecs {
  batteryCapacity: number; // kWh
  range: number; // miles
  motorPower: number; // hp
  torque: number; // lb-ft
  acceleration: number; // 0-60 mph in seconds
  topSpeed: number; // mph
  chargingSpeed: {
    ac: number; // kW
    dc: number; // kW
  };
  chargingTime: {
    home: string; // e.g., "8 hours"
    fastCharging: string; // e.g., "30 minutes"
  };
  drivetrain: 'FWD' | 'RWD' | 'AWD';
  bodyType: 'sedan' | 'suv' | 'hatchback' | 'coupe' | 'truck' | 'van';
  seatingCapacity: number;
  cargoSpace: number; // cubic feet
  weight: number; // lbs
  dimensions: {
    length: number;
    width: number;
    height: number;
    wheelbase: number;
  };
}

export interface ForumCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  threadCount: number;
  postCount: number;
  lastActivity?: {
    threadId: string;
    threadTitle: string;
    userId: string;
    userName: string;
    timestamp: string;
  };
}

export interface ForumThread {
  id: string;
  categoryId: string;
  title: string;
  content: string;
  authorId: string;
  authorName: string;
  authorAvatar?: string;
  createdAt: string;
  updatedAt: string;
  isPinned: boolean;
  isLocked: boolean;
  views: number;
  replies: number;
  votes: number;
  tags: string[];
  lastReply?: {
    userId: string;
    userName: string;
    timestamp: string;
  };
}

export interface ForumPost {
  id: string;
  threadId: string;
  content: string;
  authorId: string;
  authorName: string;
  authorAvatar?: string;
  createdAt: string;
  updatedAt?: string;
  votes: number;
  isEdited: boolean;
  parentPostId?: string; // for replies
}

export interface MarketplaceListing {
  id: string;
  type: 'vehicle' | 'parts' | 'accessories';
  title: string;
  description: string;
  price: number;
  currency: string;
  photos: string[];
  sellerId: string;
  sellerName: string;
  sellerRating: number;
  location: string;
  condition: 'new' | 'excellent' | 'good' | 'fair' | 'poor';
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
  category: string;
  subcategory?: string;
  vehicleInfo?: {
    brand: string;
    model: string;
    year: number;
    mileage: number;
    vin?: string;
  };
  partInfo?: {
    partNumber?: string;
    compatibility: string[];
    brand: string;
  };
  contactInfo: {
    phone?: string;
    email?: string;
    preferredContact: 'phone' | 'email' | 'message';
  };
}

export interface Business {
  id: string;
  name: string;
  description: string;
  category: 'dealership' | 'service' | 'charging' | 'insurance' | 'parts';
  subcategories: string[];
  logo?: string;
  photos: string[];
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  contact: {
    phone: string;
    email: string;
    website?: string;
  };
  hours: {
    [key: string]: string; // e.g., "monday": "9:00 AM - 6:00 PM"
  };
  rating: number;
  reviewCount: number;
  services: string[];
  certifications?: string[];
  specialties?: string[];
  isVerified: boolean;
}

export interface Review {
  id: string;
  businessId: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  rating: number;
  title: string;
  content: string;
  createdAt: string;
  helpful: number;
  photos?: string[];
}

export interface ActivityFeedItem {
  id: string;
  type: 'forum_post' | 'garage_update' | 'marketplace_listing' | 'user_joined' | 'review';
  userId: string;
  userName: string;
  userAvatar?: string;
  timestamp: string;
  content: {
    title: string;
    description: string;
    link?: string;
    image?: string;
  };
  engagement: {
    likes: number;
    comments: number;
    shares: number;
  };
}

export interface SearchFilters {
  category?: string;
  brand?: string[];
  priceRange?: {
    min: number;
    max: number;
  };
  location?: string;
  condition?: string[];
  year?: {
    min: number;
    max: number;
  };
  range?: {
    min: number;
    max: number;
  };
  bodyType?: string[];
  sortBy?: 'newest' | 'oldest' | 'price_low' | 'price_high' | 'popularity' | 'rating';
}

export interface NavigationItem {
  name: string;
  href: string;
  icon: string;
  description?: string;
  badge?: number;
}

export interface Stats {
  totalMembers: number;
  totalThreads: number;
  totalMessages: number;
  totalVehicles: number;
  totalListings: number;
  totalBusinesses: number;
}
