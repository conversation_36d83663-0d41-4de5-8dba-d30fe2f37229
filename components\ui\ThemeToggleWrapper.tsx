'use client';

import dynamic from 'next/dynamic';
import { Suspense } from 'react';

// Dynamically import ThemeToggle to avoid SSR issues
const ThemeToggle = dynamic(() => import('./ThemeToggle'), {
  ssr: false,
  loading: () => (
    <div className="h-10 w-10 rounded-xl bg-background-secondary border border-border animate-pulse" />
  ),
});

export default function ThemeToggleWrapper() {
  return (
    <Suspense fallback={
      <div className="h-10 w-10 rounded-xl bg-background-secondary border border-border animate-pulse" />
    }>
      <ThemeToggle />
    </Suspense>
  );
}
