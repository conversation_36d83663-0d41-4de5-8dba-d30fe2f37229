import React from 'react';
import Link from 'next/link';

const Footer: React.FC = () => {
  return (
    <footer className="bg-secondary-dark text-white mt-auto">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">⚡</span>
              </div>
              <span className="text-xl font-bold">EV Community</span>
            </div>
            <p className="text-gray-300 mb-4 max-w-md">
              The ultimate destination for electric vehicle enthusiasts. Connect, share, and discover everything about EVs.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-300 hover:text-white transition-colors">
                📘 Facebook
              </a>
              <a href="#" className="text-gray-300 hover:text-white transition-colors">
                🐦 Twitter
              </a>
              <a href="#" className="text-gray-300 hover:text-white transition-colors">
                📷 Instagram
              </a>
              <a href="#" className="text-gray-300 hover:text-white transition-colors">
                💼 LinkedIn
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Community</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/forums" className="text-gray-300 hover:text-white transition-colors">
                  Forums
                </Link>
              </li>
              <li>
                <Link href="/garage" className="text-gray-300 hover:text-white transition-colors">
                  Member Garage
                </Link>
              </li>
              <li>
                <Link href="/whats-new" className="text-gray-300 hover:text-white transition-colors">
                  What's New
                </Link>
              </li>
              <li>
                <Link href="/directory" className="text-gray-300 hover:text-white transition-colors">
                  Business Directory
                </Link>
              </li>
            </ul>
          </div>

          {/* Resources */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Resources</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/ev-listings" className="text-gray-300 hover:text-white transition-colors">
                  EV Database
                </Link>
              </li>
              <li>
                <Link href="/marketplace" className="text-gray-300 hover:text-white transition-colors">
                  Marketplace
                </Link>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-white transition-colors">
                  Charging Map
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-white transition-colors">
                  EV News
                </a>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-600 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-300 text-sm">
            © 2024 EV Community Platform. All rights reserved.
          </p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <a href="#" className="text-gray-300 hover:text-white text-sm transition-colors">
              Privacy Policy
            </a>
            <a href="#" className="text-gray-300 hover:text-white text-sm transition-colors">
              Terms of Service
            </a>
            <a href="#" className="text-gray-300 hover:text-white text-sm transition-colors">
              Contact Us
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
