import React from 'react';
import { getMarketplaceVehicles } from '@/lib/data/vehicles';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Select from '@/components/ui/Select';
import Badge from '@/components/ui/Badge';
import VehicleCard from '@/components/cards/VehicleCard';

export default function MarketplacePage() {
  const marketplaceVehicles = getMarketplaceVehicles();
  
  const categoryOptions = [
    { value: 'vehicles', label: 'Vehicles' },
    { value: 'parts', label: 'Parts & Accessories' },
    { value: 'charging', label: 'Charging Equipment' },
    { value: 'services', label: 'Services' }
  ];

  const conditionOptions = [
    { value: 'new', label: 'New' },
    { value: 'excellent', label: 'Excellent' },
    { value: 'good', label: 'Good' },
    { value: 'fair', label: 'Fair' }
  ];

  return (
    <div className="min-h-screen bg-secondary-light">
      {/* Page Header */}
      <div className="bg-white border-b border-border-light">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <h1 className="text-3xl font-bold text-foreground">Marketplace</h1>
              <p className="text-text-secondary mt-1">
                Buy and sell electric vehicles, parts, and accessories
              </p>
            </div>
            <div className="mt-4 md:mt-0">
              <Button>Create Listing</Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          
          {/* Filters Sidebar */}
          <div className="lg:col-span-1">
            <Card>
              <Card.Header>
                <Card.Title>Filters</Card.Title>
              </Card.Header>
              <Card.Content>
                <div className="space-y-4">
                  
                  {/* Search */}
                  <Input
                    placeholder="Search marketplace..."
                    leftIcon={<span>🔍</span>}
                  />

                  {/* Category Filter */}
                  <Select
                    label="Category"
                    placeholder="All Categories"
                    options={categoryOptions}
                  />

                  {/* Location */}
                  <Input
                    label="Location"
                    placeholder="City, State or ZIP"
                    leftIcon={<span>📍</span>}
                  />

                  {/* Price Range */}
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Price Range
                    </label>
                    <div className="grid grid-cols-2 gap-2">
                      <Input placeholder="Min" type="number" />
                      <Input placeholder="Max" type="number" />
                    </div>
                  </div>

                  {/* Condition */}
                  <Select
                    label="Condition"
                    placeholder="Any Condition"
                    options={conditionOptions}
                  />

                  {/* Distance */}
                  <Select
                    label="Distance"
                    placeholder="Any Distance"
                    options={[
                      { value: '25', label: 'Within 25 miles' },
                      { value: '50', label: 'Within 50 miles' },
                      { value: '100', label: 'Within 100 miles' },
                      { value: '250', label: 'Within 250 miles' },
                      { value: 'nationwide', label: 'Nationwide' }
                    ]}
                  />

                  {/* Quick Filters */}
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Quick Filters
                    </label>
                    <div className="space-y-2">
                      <label className="flex items-center">
                        <input type="checkbox" className="mr-2" />
                        <span className="text-sm">Dealer Listings</span>
                      </label>
                      <label className="flex items-center">
                        <input type="checkbox" className="mr-2" />
                        <span className="text-sm">Private Sellers</span>
                      </label>
                      <label className="flex items-center">
                        <input type="checkbox" className="mr-2" />
                        <span className="text-sm">With Photos</span>
                      </label>
                      <label className="flex items-center">
                        <input type="checkbox" className="mr-2" />
                        <span className="text-sm">Recently Listed</span>
                      </label>
                    </div>
                  </div>

                  <Button variant="outline" className="w-full">
                    Clear Filters
                  </Button>
                </div>
              </Card.Content>
            </Card>

            {/* Selling Tips */}
            <Card className="mt-6">
              <Card.Header>
                <Card.Title>💡 Selling Tips</Card.Title>
              </Card.Header>
              <Card.Content>
                <ul className="space-y-2 text-sm text-text-secondary">
                  <li>• Include high-quality photos</li>
                  <li>• Write detailed descriptions</li>
                  <li>• Price competitively</li>
                  <li>• Respond quickly to inquiries</li>
                  <li>• Be honest about condition</li>
                </ul>
              </Card.Content>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            
            {/* Category Tabs */}
            <Card className="mb-6">
              <div className="flex flex-wrap gap-2">
                <Button size="sm">All Items</Button>
                <Button variant="outline" size="sm">🚗 Vehicles</Button>
                <Button variant="outline" size="sm">🔧 Parts</Button>
                <Button variant="outline" size="sm">🔌 Charging</Button>
                <Button variant="outline" size="sm">⚙️ Services</Button>
              </div>
            </Card>

            {/* Sort and View Options */}
            <Card className="mb-6">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-text-secondary">
                    {marketplaceVehicles.length} listings found
                  </span>
                  <Badge variant="success" size="sm">
                    {marketplaceVehicles.filter(v => v.isForSale).length} active
                  </Badge>
                </div>
                <div className="flex items-center space-x-4">
                  <Select
                    options={[
                      { value: 'newest', label: 'Newest First' },
                      { value: 'price_low', label: 'Price: Low to High' },
                      { value: 'price_high', label: 'Price: High to Low' },
                      { value: 'distance', label: 'Distance' },
                      { value: 'mileage', label: 'Mileage: Low to High' }
                    ]}
                    placeholder="Sort by"
                    className="w-40"
                  />
                  <div className="flex border border-border rounded-lg">
                    <button className="p-2 bg-primary text-white rounded-l-lg">
                      <span>⊞</span>
                    </button>
                    <button className="p-2 hover:bg-secondary-light rounded-r-lg">
                      <span>☰</span>
                    </button>
                  </div>
                </div>
              </div>
            </Card>

            {/* Featured Listings */}
            <Card className="mb-6">
              <Card.Header>
                <Card.Title>⭐ Featured Listings</Card.Title>
              </Card.Header>
              <Card.Content>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {marketplaceVehicles.slice(0, 2).map((vehicle) => (
                    <VehicleCard
                      key={vehicle.id}
                      vehicle={vehicle}
                      showPrice={true}
                      showActions={true}
                      href={`/marketplace/${vehicle.id}`}
                    />
                  ))}
                </div>
              </Card.Content>
            </Card>

            {/* All Listings */}
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
              {marketplaceVehicles.map((vehicle) => (
                <VehicleCard
                  key={vehicle.id}
                  vehicle={vehicle}
                  showPrice={true}
                  showActions={true}
                  href={`/marketplace/${vehicle.id}`}
                />
              ))}
            </div>

            {/* Load More */}
            <div className="mt-8 text-center">
              <Button variant="outline">Load More Listings</Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
