import React from "react";
import { cn } from "@/lib/utils";

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "primary" | "secondary" | "outline" | "ghost" | "danger";
  size?: "sm" | "md" | "lg";
  children: React.ReactNode;
  className?: string;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    { variant = "primary", size = "md", children, className, ...props },
    ref
  ) => {
    const baseClasses =
      "hover:cursor-pointer inline-flex items-center justify-center font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden group";

    const variants = {
      primary:
        "bg-gradient-primary text-white hover:shadow-lg focus:ring-primary-100 shadow-md",
      secondary:
        "bg-background text-foreground border-2 border-border hover:bg-background-secondary hover:border-border-strong hover:shadow-md focus:ring-primary-100",
      outline:
        "border-2 border-primary text-primary hover:bg-primary hover:text-white hover:shadow-lg focus:ring-primary-100",
      ghost:
        "text-foreground-muted hover:text-foreground hover:bg-background-secondary focus:ring-primary-100",
      danger:
        "bg-gradient-to-r from-danger to-red-600 text-white hover:shadow-lg focus:ring-red-100",
    };

    const sizes = {
      sm: "px-4 py-2 text-sm rounded-xl",
      md: "px-6 py-3 text-sm rounded-xl",
      lg: "px-8 py-4 text-base rounded-2xl",
    };

    return (
      <button
        ref={ref}
        className={cn(baseClasses, variants[variant], sizes[size], className)}
        {...props}
      >
        {/* Shimmer effect for primary buttons */}
        {variant === "primary" && (
          <div className="absolute inset-0 -top-2 -bottom-2 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
        )}
        {children}
      </button>
    );
  }
);

Button.displayName = "Button";

export default Button;
