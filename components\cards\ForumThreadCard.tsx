import React from 'react';
import Link from 'next/link';
import { ForumThread } from '@/lib/types';
import { formatTimeAgo } from '@/lib/utils';
import Card from '@/components/ui/Card';
import Badge from '@/components/ui/Badge';

interface ForumThreadCardProps {
  thread: ForumThread;
  showCategory?: boolean;
  compact?: boolean;
}

const ForumThreadCard: React.FC<ForumThreadCardProps> = ({ 
  thread, 
  showCategory = true,
  compact = false 
}) => {
  if (compact) {
    return (
      <div className="flex items-start space-x-3 p-3 hover:bg-secondary-light rounded-lg transition-colors">
        <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-semibold">
          {thread.authorName.charAt(0)}
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2 mb-1">
            <span className="font-medium text-foreground">{thread.authorName}</span>
            {showCategory && (
              <>
                <span className="text-text-secondary text-sm">in</span>
                <Badge variant="primary" size="sm">{thread.categoryId}</Badge>
              </>
            )}
            <span className="text-text-secondary text-sm">•</span>
            <span className="text-text-secondary text-sm">{formatTimeAgo(thread.createdAt)}</span>
          </div>
          <Link href={`/forums/${thread.categoryId}/${thread.id}`}>
            <h4 className="font-medium text-foreground hover:text-primary transition-colors line-clamp-1">
              {thread.title}
            </h4>
          </Link>
          <p className="text-text-secondary text-sm line-clamp-2 mt-1">
            {thread.content}
          </p>
          <div className="flex items-center space-x-4 mt-2 text-sm text-text-secondary">
            <span>👍 {thread.votes}</span>
            <span>💬 {thread.replies}</span>
            <span>👁 {thread.views}</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <Card hover>
      <div className="flex items-start space-x-4">
        {/* Author Avatar */}
        <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center text-white font-semibold">
          {thread.authorName.charAt(0)}
        </div>
        
        <div className="flex-1 min-w-0">
          {/* Thread Header */}
          <div className="flex items-center space-x-2 mb-2">
            <span className="font-medium text-foreground">{thread.authorName}</span>
            {showCategory && (
              <>
                <span className="text-text-secondary text-sm">posted in</span>
                <Badge variant="primary" size="sm">{thread.categoryId}</Badge>
              </>
            )}
            <span className="text-text-secondary text-sm">•</span>
            <span className="text-text-secondary text-sm">{formatTimeAgo(thread.createdAt)}</span>
            {thread.isPinned && (
              <Badge variant="warning" size="sm">📌 Pinned</Badge>
            )}
            {thread.isLocked && (
              <Badge variant="secondary" size="sm">🔒 Locked</Badge>
            )}
          </div>

          {/* Thread Title */}
          <Link href={`/forums/${thread.categoryId}/${thread.id}`}>
            <h3 className="text-lg font-semibold text-foreground hover:text-primary transition-colors mb-2">
              {thread.title}
            </h3>
          </Link>

          {/* Thread Content Preview */}
          <p className="text-text-secondary line-clamp-3 mb-3">
            {thread.content}
          </p>

          {/* Tags */}
          {thread.tags && thread.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-3">
              {thread.tags.map((tag) => (
                <Badge key={tag} variant="secondary" size="sm">
                  {tag}
                </Badge>
              ))}
            </div>
          )}

          {/* Thread Stats */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 text-sm text-text-secondary">
              <span className="flex items-center">
                <span className="mr-1">👍</span>
                {thread.votes}
              </span>
              <span className="flex items-center">
                <span className="mr-1">💬</span>
                {thread.replies} replies
              </span>
              <span className="flex items-center">
                <span className="mr-1">👁</span>
                {thread.views} views
              </span>
            </div>

            {/* Last Reply */}
            {thread.lastReply && (
              <div className="text-sm text-text-secondary">
                Last reply by <span className="font-medium">{thread.lastReply.userName}</span>
                <span className="ml-1">{formatTimeAgo(thread.lastReply.timestamp)}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default ForumThreadCard;
