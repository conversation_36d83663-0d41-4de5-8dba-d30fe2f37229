import React from 'react';
import { getAllVehicles } from '@/lib/data/vehicles';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Select from '@/components/ui/Select';
import VehicleCard from '@/components/cards/VehicleCard';

export default function EVListingsPage() {
  const vehicles = getAllVehicles();
  
  // Get unique brands for filter
  const brands = Array.from(new Set(vehicles.map(v => v.brand))).sort();
  
  const brandOptions = brands.map(brand => ({ value: brand, label: brand }));
  const bodyTypeOptions = [
    { value: 'sedan', label: 'Sedan' },
    { value: 'suv', label: 'SUV' },
    { value: 'hatchback', label: 'Hatchback' },
    { value: 'coupe', label: 'Coupe' },
    { value: 'truck', label: 'Truck' },
    { value: 'van', label: 'Van' }
  ];

  return (
    <div className="min-h-screen bg-secondary-light">
      {/* Page Header */}
      <div className="bg-white border-b border-border-light">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <h1 className="text-3xl font-bold text-foreground">EV Database</h1>
              <p className="text-text-secondary mt-1">
                Explore comprehensive electric vehicle specifications and comparisons
              </p>
            </div>
            <div className="mt-4 md:mt-0 flex gap-3">
              <Button variant="outline">Compare Vehicles</Button>
              <Button>Add to Garage</Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          
          {/* Filters Sidebar */}
          <div className="lg:col-span-1">
            <Card>
              <Card.Header>
                <Card.Title>Filters</Card.Title>
              </Card.Header>
              <Card.Content>
                <div className="space-y-4">
                  
                  {/* Search */}
                  <Input
                    placeholder="Search vehicles..."
                    leftIcon={<span>🔍</span>}
                  />

                  {/* Brand Filter */}
                  <Select
                    label="Brand"
                    placeholder="All Brands"
                    options={brandOptions}
                  />

                  {/* Body Type Filter */}
                  <Select
                    label="Body Type"
                    placeholder="All Types"
                    options={bodyTypeOptions}
                  />

                  {/* Price Range */}
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Price Range
                    </label>
                    <div className="grid grid-cols-2 gap-2">
                      <Input placeholder="Min" type="number" />
                      <Input placeholder="Max" type="number" />
                    </div>
                  </div>

                  {/* Range Filter */}
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Range (miles)
                    </label>
                    <div className="grid grid-cols-2 gap-2">
                      <Input placeholder="Min" type="number" />
                      <Input placeholder="Max" type="number" />
                    </div>
                  </div>

                  {/* Year Filter */}
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Year
                    </label>
                    <div className="grid grid-cols-2 gap-2">
                      <Input placeholder="From" type="number" />
                      <Input placeholder="To" type="number" />
                    </div>
                  </div>

                  {/* Quick Filters */}
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Quick Filters
                    </label>
                    <div className="space-y-2">
                      <label className="flex items-center">
                        <input type="checkbox" className="mr-2" />
                        <span className="text-sm">Fast Charging (150kW+)</span>
                      </label>
                      <label className="flex items-center">
                        <input type="checkbox" className="mr-2" />
                        <span className="text-sm">Long Range (300+ mi)</span>
                      </label>
                      <label className="flex items-center">
                        <input type="checkbox" className="mr-2" />
                        <span className="text-sm">AWD Available</span>
                      </label>
                      <label className="flex items-center">
                        <input type="checkbox" className="mr-2" />
                        <span className="text-sm">Under $50k</span>
                      </label>
                    </div>
                  </div>

                  <Button variant="outline" className="w-full">
                    Clear Filters
                  </Button>
                </div>
              </Card.Content>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            
            {/* Sort and View Options */}
            <Card className="mb-6">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-text-secondary">
                    Showing {vehicles.length} vehicles
                  </span>
                </div>
                <div className="flex items-center space-x-4">
                  <Select
                    options={[
                      { value: 'newest', label: 'Newest First' },
                      { value: 'oldest', label: 'Oldest First' },
                      { value: 'price_low', label: 'Price: Low to High' },
                      { value: 'price_high', label: 'Price: High to Low' },
                      { value: 'range_high', label: 'Range: High to Low' },
                      { value: 'brand', label: 'Brand A-Z' }
                    ]}
                    placeholder="Sort by"
                    className="w-40"
                  />
                  <div className="flex border border-border rounded-lg">
                    <button className="p-2 bg-primary text-white rounded-l-lg">
                      <span>⊞</span>
                    </button>
                    <button className="p-2 hover:bg-secondary-light rounded-r-lg">
                      <span>☰</span>
                    </button>
                  </div>
                </div>
              </div>
            </Card>

            {/* Vehicle Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
              {vehicles.map((vehicle) => (
                <VehicleCard
                  key={vehicle.id}
                  vehicle={vehicle}
                  href={`/ev-listings/${vehicle.id}`}
                  showActions={true}
                />
              ))}
            </div>

            {/* Pagination */}
            <div className="mt-8 flex justify-center">
              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm" disabled>
                  Previous
                </Button>
                <Button size="sm">1</Button>
                <Button variant="outline" size="sm">2</Button>
                <Button variant="outline" size="sm">3</Button>
                <Button variant="outline" size="sm">
                  Next
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
