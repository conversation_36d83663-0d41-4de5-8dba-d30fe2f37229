import Link from "next/link";
import Card from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import { getRecentThreads, getTrendingThreads } from "@/lib/data/forums";
import { getMarketplaceVehicles } from "@/lib/data/vehicles";
import { formatTimeAgo, formatPrice } from "@/lib/utils";

export default function Home() {
  const recentThreads = getRecentThreads(5);
  const trendingThreads = getTrendingThreads(3);
  const recentListings = getMarketplaceVehicles().slice(0, 3);

  const stats = {
    totalMembers: 12847,
    totalThreads: 3456,
    totalMessages: 28934,
    totalVehicles: 1567,
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background-secondary to-background-tertiary">
      {/* Hero Section */}
      <section className="relative hero-gradient text-white py-24 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent"></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="text-center">
            <div className="inline-flex items-center px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full text-sm font-medium mb-8 border border-white/20">
              <span className="mr-2">⚡</span>
              The Future of Transportation is Electric
            </div>

            <h1 className="text-5xl md:text-7xl font-black mb-8 bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent leading-tight">
              Welcome to the
              <br />
              <span className="text-accent-light">EV Community</span>
            </h1>

            <p className="text-xl md:text-2xl mb-12 text-blue-100 max-w-3xl mx-auto leading-relaxed">
              Connect with fellow enthusiasts, discover the latest electric
              vehicles, and share your journey towards a sustainable future
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <Button size="lg" className="text-lg px-8 py-4 shadow-2xl">
                <span className="mr-2">🚀</span>
                Join the Community
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-2 border-white/30 text-white hover:bg-white/10 backdrop-blur-sm text-lg px-8 py-4"
              >
                <span className="mr-2">💬</span>
                Explore Forums
              </Button>
            </div>
          </div>
        </div>

        {/* Floating Elements */}
        <div className="absolute top-20 left-10 w-20 h-20 bg-white/5 rounded-full animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-32 h-32 bg-accent/20 rounded-full animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 right-20 w-16 h-16 bg-white/10 rounded-full animate-pulse delay-500"></div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-background relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-foreground mb-4">
              Everything You Need for Your EV Journey
            </h2>
            <p className="text-xl text-foreground-muted max-w-2xl mx-auto">
              From community discussions to vehicle comparisons, we&apos;ve got
              you covered
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center group">
              <div className="w-16 h-16 bg-gradient-primary rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                <span className="text-2xl">💬</span>
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-3">
                Active Forums
              </h3>
              <p className="text-foreground-muted">
                Connect with thousands of EV enthusiasts and get answers to all
                your questions
              </p>
            </div>

            <div className="text-center group">
              <div className="w-16 h-16 bg-gradient-accent rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                <span className="text-2xl">🚗</span>
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-3">
                EV Database
              </h3>
              <p className="text-foreground-muted">
                Comprehensive database of electric vehicles with detailed
                specifications and comparisons
              </p>
            </div>

            <div className="text-center group">
              <div className="w-16 h-16 bg-gradient-to-r from-warning to-orange-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                <span className="text-2xl">🛒</span>
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-3">
                Marketplace
              </h3>
              <p className="text-foreground-muted">
                Buy and sell EVs, parts, and accessories in our trusted
                marketplace
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-20 ">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* What's New Feed */}
            <div className="lg:col-span-2">
              <Card>
                <Card.Header>
                  <div className="flex items-center justify-between">
                    <Card.Title>What&apos;s New</Card.Title>
                    <Link href="/whats-new">
                      <Button variant="ghost" size="sm">
                        View All
                      </Button>
                    </Link>
                  </div>
                </Card.Header>
                <Card.Content>
                  <div className="space-y-4">
                    {recentThreads.map((thread) => (
                      <div
                        key={thread.id}
                        className="flex items-start space-x-3 p-3 rounded-lg hover:bg-secondary-light transition-colors"
                      >
                        <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-semibold">
                          {thread.authorName.charAt(0)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2">
                            <span className="font-medium text-foreground">
                              {thread.authorName}
                            </span>
                            <span className="text-text-secondary text-sm">
                              posted in
                            </span>
                            <span className="text-primary text-sm font-medium">
                              {thread.categoryId}
                            </span>
                            <span className="text-text-secondary text-sm">
                              •
                            </span>
                            <span className="text-text-secondary text-sm">
                              {formatTimeAgo(thread.createdAt)}
                            </span>
                          </div>
                          <Link
                            href={`/forums/${thread.categoryId}/${thread.id}`}
                          >
                            <h4 className="font-medium text-foreground hover:text-primary transition-colors line-clamp-1">
                              {thread.title}
                            </h4>
                          </Link>
                          <p className="text-text-secondary text-sm line-clamp-2 mt-1">
                            {thread.content}
                          </p>
                          <div className="flex items-center space-x-4 mt-2 text-sm text-text-secondary">
                            <span>👍 {thread.votes}</span>
                            <span>💬 {thread.replies}</span>
                            <span>👁 {thread.views}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </Card.Content>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Trending Discussions */}
              <Card>
                <Card.Header>
                  <Card.Title>🔥 Trending</Card.Title>
                </Card.Header>
                <Card.Content>
                  <div className="space-y-3">
                    {trendingThreads.map((thread, index) => (
                      <div
                        key={thread.id}
                        className="flex items-start space-x-3"
                      >
                        <div className="w-6 h-6 bg-accent-green rounded-full flex items-center justify-center text-white text-xs font-bold">
                          {index + 1}
                        </div>
                        <div className="flex-1 min-w-0">
                          <Link
                            href={`/forums/${thread.categoryId}/${thread.id}`}
                          >
                            <h5 className="font-medium text-foreground hover:text-primary transition-colors line-clamp-2 text-sm">
                              {thread.title}
                            </h5>
                          </Link>
                          <div className="flex items-center space-x-2 mt-1 text-xs text-text-secondary">
                            <span>👍 {thread.votes}</span>
                            <span>💬 {thread.replies}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </Card.Content>
              </Card>

              {/* Recent Marketplace */}
              <Card>
                <Card.Header>
                  <div className="flex items-center justify-between">
                    <Card.Title>🛒 Marketplace</Card.Title>
                    <Link href="/marketplace">
                      <Button variant="ghost" size="sm">
                        View All
                      </Button>
                    </Link>
                  </div>
                </Card.Header>
                <Card.Content>
                  <div className="space-y-3">
                    {recentListings.map((vehicle) => (
                      <div
                        key={vehicle.id}
                        className="border border-border-light rounded-lg p-3 hover:shadow-md transition-shadow"
                      >
                        <div className="flex items-center space-x-3">
                          <div className="w-12 h-12 bg-secondary-light rounded-lg flex items-center justify-center">
                            <span className="text-lg">🚗</span>
                          </div>
                          <div className="flex-1 min-w-0">
                            <h5 className="font-medium text-foreground line-clamp-1">
                              {vehicle.year} {vehicle.brand} {vehicle.model}
                            </h5>
                            <p className="text-primary font-semibold">
                              {formatPrice(vehicle.price || 0)}
                            </p>
                            <p className="text-text-secondary text-sm">
                              {vehicle.location}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </Card.Content>
              </Card>

              {/* Quick Actions */}
              <Card>
                <Card.Header>
                  <Card.Title>Quick Actions</Card.Title>
                </Card.Header>
                <Card.Content>
                  <div className="space-y-3">
                    <Link href="/forums">
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full justify-start"
                      >
                        💬 Start a Discussion
                      </Button>
                    </Link>
                    <Link href="/garage">
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full justify-start"
                      >
                        🏠 Add to Garage
                      </Button>
                    </Link>
                    <Link href="/marketplace">
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full justify-start"
                      >
                        🛒 List for Sale
                      </Button>
                    </Link>
                    <Link href="/ev-listings">
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full justify-start"
                      >
                        🚗 Browse EVs
                      </Button>
                    </Link>
                  </div>
                </Card.Content>
              </Card>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
