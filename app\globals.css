@import "tailwindcss";
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap");

:root {
  /* Light Theme - Modern EV Community Design System */
  --primary: #3b82f6;
  --primary-dark: #1d4ed8;
  --primary-light: #93c5fd;
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-900: #1e3a8a;

  --secondary: #64748b;
  --secondary-50: #f8fafc;
  --secondary-100: #f1f5f9;
  --secondary-200: #e2e8f0;
  --secondary-300: #cbd5e1;
  --secondary-400: #94a3b8;
  --secondary-500: #64748b;
  --secondary-600: #475569;
  --secondary-700: #334155;
  --secondary-800: #1e293b;
  --secondary-900: #0f172a;

  --accent: #10b981;
  --accent-dark: #059669;
  --accent-light: #6ee7b7;

  --background: #ffffff;
  --background-secondary: #f8fafc;
  --background-tertiary: #f1f5f9;
  --background-elevated: #ffffff;
  --foreground: #0f172a;
  --foreground-secondary: #334155;
  --foreground-muted: #64748b;

  --border: #e2e8f0;
  --border-light: #f1f5f9;
  --border-strong: #cbd5e1;

  --success: #10b981;
  --warning: #f59e0b;
  --danger: #ef4444;
  --info: #06b6d4;

  --gradient-primary: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  --gradient-secondary: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  --gradient-accent: linear-gradient(135deg, #10b981 0%, #059669 100%);
  --gradient-hero: linear-gradient(
    135deg,
    #3b82f6 0%,
    #1d4ed8 50%,
    #1e3a8a 100%
  );

  --glass-bg: rgba(255, 255, 255, 0.8);
  --glass-border: rgba(255, 255, 255, 0.2);
  --dropdown-bg: rgba(255, 255, 255, 0.95);
}

/* Dark Theme */
[data-theme="dark"] {
  --primary: #60a5fa;
  --primary-dark: #3b82f6;
  --primary-light: #93c5fd;
  --primary-50: #1e3a8a;
  --primary-100: #1d4ed8;
  --primary-500: #60a5fa;
  --primary-600: #3b82f6;
  --primary-900: #93c5fd;

  --secondary: #94a3b8;
  --secondary-50: #0f172a;
  --secondary-100: #1e293b;
  --secondary-200: #334155;
  --secondary-300: #475569;
  --secondary-400: #64748b;
  --secondary-500: #94a3b8;
  --secondary-600: #cbd5e1;
  --secondary-700: #e2e8f0;
  --secondary-800: #f1f5f9;
  --secondary-900: #f8fafc;

  --accent: #34d399;
  --accent-dark: #10b981;
  --accent-light: #6ee7b7;

  --background: #0f172a;
  --background-secondary: #1e293b;
  --background-tertiary: #334155;
  --background-elevated: #1e293b;
  --foreground: #f8fafc;
  --foreground-secondary: #e2e8f0;
  --foreground-muted: #94a3b8;

  --border: #334155;
  --border-light: #f3f3f312;
  --border-strong: #64748b;

  --success: #34d399;
  --warning: #fbbf24;
  --danger: #f87171;
  --info: #22d3ee;

  --gradient-primary: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
  --gradient-secondary: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  --gradient-accent: linear-gradient(135deg, #34d399 0%, #10b981 100%);
  --gradient-hero: linear-gradient(
    135deg,
    #1e3a8a 0%,
    #1d4ed8 50%,
    #3b82f6 100%
  );

  --glass-bg: rgba(30, 41, 59, 0.8);
  --glass-border: rgba(148, 163, 184, 0.2);
  --dropdown-bg: rgba(30, 41, 59, 0.95);
}

@theme inline {
  --color-primary: var(--primary);
  --color-primary-dark: var(--primary-dark);
  --color-primary-light: var(--primary-light);
  --color-primary-50: var(--primary-50);
  --color-primary-100: var(--primary-100);
  --color-primary-500: var(--primary-500);
  --color-primary-600: var(--primary-600);
  --color-primary-900: var(--primary-900);

  --color-secondary: var(--secondary);
  --color-secondary-50: var(--secondary-50);
  --color-secondary-100: var(--secondary-100);
  --color-secondary-200: var(--secondary-200);
  --color-secondary-300: var(--secondary-300);
  --color-secondary-400: var(--secondary-400);
  --color-secondary-500: var(--secondary-500);
  --color-secondary-600: var(--secondary-600);
  --color-secondary-700: var(--secondary-700);
  --color-secondary-800: var(--secondary-800);
  --color-secondary-900: var(--secondary-900);

  --color-accent: var(--accent);
  --color-accent-dark: var(--accent-dark);
  --color-accent-light: var(--accent-light);

  --color-background: var(--background);
  --color-background-secondary: var(--background-secondary);
  --color-background-tertiary: var(--background-tertiary);
  --color-background-elevated: var(--background-elevated);
  --color-foreground: var(--foreground);
  --color-foreground-secondary: var(--foreground-secondary);
  --color-foreground-muted: var(--foreground-muted);

  --color-border: var(--border);
  --color-border-light: var(--border-light);
  --color-border-strong: var(--border-strong);

  --color-success: var(--success);
  --color-warning: var(--warning);
  --color-danger: var(--danger);
  --color-info: var(--info);

  --font-sans: "Inter", system-ui, -apple-system, sans-serif;
  --font-mono: var(--font-geist-mono);

  --radius-sm: 0.5rem;
  --radius: 0.75rem;
  --radius-lg: 1rem;
  --radius-xl: 1.5rem;
  --radius-2xl: 2rem;

  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-md: 0 10px 15px -3px rgb(0 0 0 / 0.1),
    0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 20px 25px -5px rgb(0 0 0 / 0.1),
    0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0f172a;
    --background-secondary: #1e293b;
    --background-tertiary: #334155;
    --foreground: #f8fafc;
    --foreground-secondary: #e2e8f0;
    --foreground-muted: #94a3b8;
    --border: #334155;
    --border-light: #f3f3f312;
    --border-strong: #64748b;
  }
}

* {
  box-sizing: border-box;
}

/* Smooth theme transitions */
html {
  transition: background-color 0.3s ease, color 0.3s ease;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: "Inter", system-ui, -apple-system, sans-serif;
  font-size: 16px;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: "cv02", "cv03", "cv04", "cv11";
  margin: 0;
  padding: 0;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Modern Typography Scale */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 700;
  line-height: 1.2;
  margin: 0;
  letter-spacing: -0.025em;
}

h1 {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  letter-spacing: -0.05em;
}

h2 {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 700;
}

h3 {
  font-size: clamp(1.5rem, 3vw, 2rem);
  font-weight: 600;
}

h4 {
  font-size: clamp(1.25rem, 2.5vw, 1.5rem);
  font-weight: 600;
}

h5 {
  font-size: 1.125rem;
  font-weight: 600;
}

h6 {
  font-size: 1rem;
  font-weight: 600;
}

/* Modern Utility Classes */
.text-muted {
  color: var(--foreground-muted);
}

.text-secondary {
  color: var(--foreground-secondary);
}

.bg-gradient-primary {
  background: var(--gradient-primary);
}

.bg-gradient-secondary {
  background: var(--gradient-secondary);
}

.bg-gradient-accent {
  background: var(--gradient-accent);
}

/* Modern Card Component */
.card {
  background: var(--background-elevated);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.card:hover {
  box-shadow: var(--shadow-lg);
  border-color: var(--border-strong);
}

.card-elevated {
  box-shadow: var(--shadow-md);
  border: none;
}

.card-elevated:hover {
  box-shadow: var(--shadow-xl);
}

/* Modern Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-lg);
  font-weight: 600;
  font-size: 0.875rem;
  line-height: 1.25rem;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: none;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow);
}

.btn-primary:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}

.btn-secondary {
  background: var(--background);
  color: var(--foreground);
  border: 2px solid var(--border);
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
  background: var(--background-secondary);
  border-color: var(--border-strong);
  box-shadow: var(--shadow);
  transform: translateY(-1px);
}

/* Glass morphism effect */
.glass {
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

/* Hero gradient utilities */
.bg-gradient-hero {
  background: var(--gradient-hero);
}

/* Hero section with theme-aware gradient */
.hero-gradient {
  /* Light theme: Blue gradient */
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 50%, #1e3a8a 100%);
  transition: background 0.3s ease;
}

/* Dark theme: Different gradient */
[data-theme="dark"] .hero-gradient {
  background: linear-gradient(135deg, #1e3a8a 0%, #1d4ed8 50%, #3b82f6 100%);
}

/* Smooth animations */
.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Focus styles */
.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px var(--primary-100);
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-strong);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-500);
}
