import { Vehicle, VehicleSpecs } from '../types';

export const vehicleDatabase: Vehicle[] = [
  {
    id: 'v1',
    userId: '1',
    brand: 'Tesla',
    model: 'Model 3',
    year: 2023,
    trim: 'Long Range',
    color: 'Pearl White',
    photos: ['/vehicles/tesla-model3-1.jpg', '/vehicles/tesla-model3-2.jpg'],
    specifications: {
      batteryCapacity: 75,
      range: 358,
      motorPower: 346,
      torque: 389,
      acceleration: 4.2,
      topSpeed: 145,
      chargingSpeed: { ac: 11, dc: 250 },
      chargingTime: { home: '8 hours', fastCharging: '25 minutes' },
      drivetrain: 'RWD',
      bodyType: 'sedan',
      seatingCapacity: 5,
      cargoSpace: 15,
      weight: 4048,
      dimensions: { length: 184.8, width: 72.8, height: 56.8, wheelbase: 113.2 }
    },
    description: 'My daily driver. Love the efficiency and tech features.',
    isForSale: false
  },
  {
    id: 'v2',
    userId: '3',
    brand: 'BMW',
    model: 'iX',
    year: 2023,
    trim: 'xDrive50',
    color: 'Storm Bay',
    photos: ['/vehicles/bmw-ix-1.jpg', '/vehicles/bmw-ix-2.jpg'],
    specifications: {
      batteryCapacity: 111.5,
      range: 324,
      motorPower: 516,
      torque: 564,
      acceleration: 4.6,
      topSpeed: 124,
      chargingSpeed: { ac: 11, dc: 195 },
      chargingTime: { home: '11 hours', fastCharging: '35 minutes' },
      drivetrain: 'AWD',
      bodyType: 'suv',
      seatingCapacity: 5,
      cargoSpace: 35.5,
      weight: 5769,
      dimensions: { length: 195.0, width: 77.4, height: 66.8, wheelbase: 118.1 }
    },
    description: 'Luxury meets sustainability. Perfect for family trips.',
    isForSale: false
  },
  {
    id: 'v3',
    userId: '4',
    brand: 'Nissan',
    model: 'Leaf',
    year: 2022,
    trim: 'SV Plus',
    color: 'Electric Blue',
    photos: ['/vehicles/nissan-leaf-1.jpg', '/vehicles/nissan-leaf-2.jpg'],
    specifications: {
      batteryCapacity: 62,
      range: 226,
      motorPower: 214,
      torque: 250,
      acceleration: 7.4,
      topSpeed: 98,
      chargingSpeed: { ac: 6.6, dc: 100 },
      chargingTime: { home: '11.5 hours', fastCharging: '45 minutes' },
      drivetrain: 'FWD',
      bodyType: 'hatchback',
      seatingCapacity: 5,
      cargoSpace: 23.6,
      weight: 3538,
      dimensions: { length: 176.4, width: 70.5, height: 61.0, wheelbase: 106.3 }
    },
    description: 'Reliable and affordable. Great for city driving.',
    isForSale: false
  },
  {
    id: 'v4',
    userId: '6',
    brand: 'Audi',
    model: 'e-tron',
    year: 2023,
    trim: 'Premium Plus',
    color: 'Glacier White',
    photos: ['/vehicles/audi-etron-1.jpg', '/vehicles/audi-etron-2.jpg'],
    specifications: {
      batteryCapacity: 95,
      range: 222,
      motorPower: 402,
      torque: 490,
      acceleration: 5.5,
      topSpeed: 124,
      chargingSpeed: { ac: 11, dc: 150 },
      chargingTime: { home: '9 hours', fastCharging: '30 minutes' },
      drivetrain: 'AWD',
      bodyType: 'suv',
      seatingCapacity: 5,
      cargoSpace: 28.5,
      weight: 5754,
      dimensions: { length: 193.0, width: 76.3, height: 65.6, wheelbase: 115.1 }
    },
    description: 'Premium luxury EV with excellent build quality.',
    isForSale: false
  },
  {
    id: 'v5',
    userId: '7',
    brand: 'Ford',
    model: 'Mustang Mach-E',
    year: 2023,
    trim: 'Premium',
    color: 'Rapid Red',
    photos: ['/vehicles/ford-mache-1.jpg', '/vehicles/ford-mache-2.jpg'],
    specifications: {
      batteryCapacity: 88,
      range: 312,
      motorPower: 346,
      torque: 428,
      acceleration: 5.2,
      topSpeed: 111,
      chargingSpeed: { ac: 10.5, dc: 150 },
      chargingTime: { home: '8.5 hours', fastCharging: '38 minutes' },
      drivetrain: 'RWD',
      bodyType: 'suv',
      seatingCapacity: 5,
      cargoSpace: 29.7,
      weight: 4394,
      dimensions: { length: 185.9, width: 74.1, height: 63.5, wheelbase: 117.5 }
    },
    description: 'American muscle meets electric power. Love the performance!',
    isForSale: false
  },
  {
    id: 'v6',
    userId: '8',
    brand: 'Hyundai',
    model: 'Ioniq 5',
    year: 2023,
    trim: 'Limited',
    color: 'Cyber Gray',
    photos: ['/vehicles/hyundai-ioniq5-1.jpg', '/vehicles/hyundai-ioniq5-2.jpg'],
    specifications: {
      batteryCapacity: 77.4,
      range: 303,
      motorPower: 320,
      torque: 446,
      acceleration: 5.2,
      topSpeed: 115,
      chargingSpeed: { ac: 11, dc: 235 },
      chargingTime: { home: '7 hours', fastCharging: '18 minutes' },
      drivetrain: 'AWD',
      bodyType: 'suv',
      seatingCapacity: 5,
      cargoSpace: 27.2,
      weight: 4662,
      dimensions: { length: 182.5, width: 74.4, height: 63.0, wheelbase: 118.1 }
    },
    description: 'Ultra-fast charging and futuristic design. Amazing tech!',
    isForSale: false
  },
  {
    id: 'v7',
    userId: '10',
    brand: 'Volkswagen',
    model: 'ID.4',
    year: 2023,
    trim: 'Pro S',
    color: 'Moonstone Gray',
    photos: ['/vehicles/vw-id4-1.jpg', '/vehicles/vw-id4-2.jpg'],
    specifications: {
      batteryCapacity: 82,
      range: 275,
      motorPower: 201,
      torque: 229,
      acceleration: 7.5,
      topSpeed: 99,
      chargingSpeed: { ac: 11, dc: 135 },
      chargingTime: { home: '7.5 hours', fastCharging: '36 minutes' },
      drivetrain: 'RWD',
      bodyType: 'suv',
      seatingCapacity: 5,
      cargoSpace: 30.3,
      weight: 4564,
      dimensions: { length: 180.5, width: 73.3, height: 64.4, wheelbase: 108.8 }
    },
    description: 'Solid German engineering. Great value for money.',
    isForSale: false
  }
];

// Marketplace vehicles (for sale)
export const marketplaceVehicles: Vehicle[] = [
  {
    id: 'mv1',
    userId: '2',
    brand: 'Tesla',
    model: 'Model S',
    year: 2021,
    trim: 'Plaid',
    color: 'Deep Blue Metallic',
    photos: ['/vehicles/tesla-models-plaid-1.jpg'],
    specifications: {
      batteryCapacity: 100,
      range: 396,
      motorPower: 1020,
      torque: 1050,
      acceleration: 1.99,
      topSpeed: 200,
      chargingSpeed: { ac: 11, dc: 250 },
      chargingTime: { home: '12 hours', fastCharging: '30 minutes' },
      drivetrain: 'AWD',
      bodyType: 'sedan',
      seatingCapacity: 5,
      cargoSpace: 28,
      weight: 4766,
      dimensions: { length: 196.0, width: 77.3, height: 56.9, wheelbase: 116.5 }
    },
    description: 'Incredible performance sedan. Barely used, garage kept.',
    isForSale: true,
    price: 89000,
    mileage: 8500,
    condition: 'excellent',
    location: 'Austin, TX',
    contactInfo: '<EMAIL>'
  },
  {
    id: 'mv2',
    userId: '5',
    brand: 'Lucid',
    model: 'Air',
    year: 2022,
    trim: 'Dream Edition',
    color: 'Stellar White',
    photos: ['/vehicles/lucid-air-1.jpg'],
    specifications: {
      batteryCapacity: 113,
      range: 516,
      motorPower: 1111,
      torque: 1390,
      acceleration: 2.5,
      topSpeed: 168,
      chargingSpeed: { ac: 19.2, dc: 300 },
      chargingTime: { home: '9 hours', fastCharging: '20 minutes' },
      drivetrain: 'AWD',
      bodyType: 'sedan',
      seatingCapacity: 5,
      cargoSpace: 22,
      weight: 5236,
      dimensions: { length: 195.8, width: 76.3, height: 55.4, wheelbase: 116.5 }
    },
    description: 'Luxury redefined. Ultimate range and performance.',
    isForSale: true,
    price: 145000,
    mileage: 3200,
    condition: 'excellent',
    location: 'Los Angeles, CA',
    contactInfo: '<EMAIL>'
  }
];

export const getAllVehicles = (): Vehicle[] => {
  return [...vehicleDatabase, ...marketplaceVehicles];
};

export const getVehicleById = (id: string): Vehicle | undefined => {
  return getAllVehicles().find(vehicle => vehicle.id === id);
};

export const getVehiclesByBrand = (brand: string): Vehicle[] => {
  return getAllVehicles().filter(vehicle => 
    vehicle.brand.toLowerCase() === brand.toLowerCase()
  );
};

export const getMarketplaceVehicles = (): Vehicle[] => {
  return getAllVehicles().filter(vehicle => vehicle.isForSale);
};

export const getUserVehicles = (userId: string): Vehicle[] => {
  return getAllVehicles().filter(vehicle => vehicle.userId === userId);
};
