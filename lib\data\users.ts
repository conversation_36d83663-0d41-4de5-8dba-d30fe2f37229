import { User } from '../types';

export const users: User[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/avatars/alex.jpg',
    role: 'community',
    joinDate: '2023-01-15',
    location: 'San Francisco, CA',
    bio: 'Tesla Model 3 owner since 2021. Love road trips and sustainable transportation.',
    garage: [],
    forumActivity: {
      posts: 156,
      threads: 23,
      reputation: 89
    }
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/avatars/sarah.jpg',
    role: 'moderator',
    joinDate: '2022-08-20',
    location: 'Austin, TX',
    bio: 'EV enthusiast and forum moderator. Helping the community grow!',
    garage: [],
    forumActivity: {
      posts: 342,
      threads: 67,
      reputation: 245
    }
  },
  {
    id: '3',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/avatars/mike.jpg',
    role: 'community',
    joinDate: '2023-03-10',
    location: 'Denver, CO',
    bio: 'BMW iX owner. Former <PERSON><PERSON> car mechanic turned EV advocate.',
    garage: [],
    forumActivity: {
      posts: 89,
      threads: 12,
      reputation: 67
    }
  },
  {
    id: '4',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/avatars/emily.jpg',
    role: 'community',
    joinDate: '2023-05-22',
    location: 'Seattle, WA',
    bio: 'Nissan Leaf driver. Passionate about clean energy and environmental sustainability.',
    garage: [],
    forumActivity: {
      posts: 78,
      threads: 15,
      reputation: 45
    }
  },
  {
    id: '5',
    name: 'David Kim',
    email: '<EMAIL>',
    avatar: '/avatars/david.jpg',
    role: 'business',
    joinDate: '2022-11-05',
    location: 'Los Angeles, CA',
    bio: 'Owner of EV Solutions LA. Specializing in EV charging installations.',
    garage: [],
    forumActivity: {
      posts: 234,
      threads: 45,
      reputation: 178
    }
  },
  {
    id: '6',
    name: 'Lisa Thompson',
    email: '<EMAIL>',
    avatar: '/avatars/lisa.jpg',
    role: 'community',
    joinDate: '2023-02-14',
    location: 'Miami, FL',
    bio: 'Audi e-tron owner. Love the luxury EV experience.',
    garage: [],
    forumActivity: {
      posts: 67,
      threads: 8,
      reputation: 34
    }
  },
  {
    id: '7',
    name: 'James Wilson',
    email: '<EMAIL>',
    avatar: '/avatars/james.jpg',
    role: 'community',
    joinDate: '2023-04-18',
    location: 'Chicago, IL',
    bio: 'Ford Mustang Mach-E enthusiast. Weekend road tripper.',
    garage: [],
    forumActivity: {
      posts: 123,
      threads: 19,
      reputation: 78
    }
  },
  {
    id: '8',
    name: 'Maria Garcia',
    email: '<EMAIL>',
    avatar: '/avatars/maria.jpg',
    role: 'community',
    joinDate: '2023-01-30',
    location: 'Phoenix, AZ',
    bio: 'Hyundai Ioniq 5 owner. Solar panel enthusiast and clean energy advocate.',
    garage: [],
    forumActivity: {
      posts: 145,
      threads: 22,
      reputation: 92
    }
  },
  {
    id: '9',
    name: 'Robert Brown',
    email: '<EMAIL>',
    avatar: '/avatars/robert.jpg',
    role: 'moderator',
    joinDate: '2022-06-12',
    location: 'Portland, OR',
    bio: 'Long-time EV advocate and community moderator. Here to help!',
    garage: [],
    forumActivity: {
      posts: 456,
      threads: 89,
      reputation: 312
    }
  },
  {
    id: '10',
    name: 'Jennifer Lee',
    email: '<EMAIL>',
    avatar: '/avatars/jennifer.jpg',
    role: 'community',
    joinDate: '2023-06-08',
    location: 'Boston, MA',
    bio: 'Volkswagen ID.4 owner. New to EVs but loving the experience!',
    garage: [],
    forumActivity: {
      posts: 34,
      threads: 6,
      reputation: 18
    }
  }
];

export const getUserById = (id: string): User | undefined => {
  return users.find(user => user.id === id);
};

export const getUsersByRole = (role: User['role']): User[] => {
  return users.filter(user => user.role === role);
};

export const getTopContributors = (limit: number = 5): User[] => {
  return users
    .sort((a, b) => b.forumActivity.reputation - a.forumActivity.reputation)
    .slice(0, limit);
};
