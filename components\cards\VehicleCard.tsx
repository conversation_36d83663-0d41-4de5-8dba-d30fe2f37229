import React from "react";
import Link from "next/link";
import { Vehicle } from "@/lib/types";
import { formatPrice, formatMileage } from "@/lib/utils";
import Card from "@/components/ui/Card";
import Badge from "@/components/ui/Badge";
import Button from "@/components/ui/Button";

interface VehicleCardProps {
  vehicle: Vehicle;
  showPrice?: boolean;
  showActions?: boolean;
  href?: string;
}

const VehicleCard: React.FC<VehicleCardProps> = ({
  vehicle,
  showPrice = false,
  showActions = false,
  href,
}) => {
  const cardContent = (
    <Card hover={!!href} padding="none" className="group overflow-hidden">
      {/* Vehicle Image */}
      <div className="aspect-video bg-gradient-to-br from-secondary-100 to-secondary-200 rounded-t-2xl flex items-center justify-center relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary-500/10 to-accent/10"></div>
        <span className="text-5xl relative z-10 group-hover:scale-110 transition-transform duration-300">
          🚗
        </span>
        {vehicle.isForSale && (
          <div className="absolute top-3 right-3">
            <Badge variant="success" size="sm" className="shadow-lg">
              For Sale
            </Badge>
          </div>
        )}
      </div>

      <div className="p-6">
        {/* Vehicle Title */}
        <div className="flex items-start justify-between mb-4">
          <div>
            <h3 className="font-bold text-foreground text-lg group-hover:text-primary transition-colors">
              {vehicle.year} {vehicle.brand} {vehicle.model}
            </h3>
            {vehicle.trim && (
              <p className="text-sm text-foreground-muted font-medium">
                {vehicle.trim}
              </p>
            )}
          </div>
        </div>

        {/* Key Specs */}
        <div className="grid grid-cols-2 gap-2 mb-3 text-sm">
          <div>
            <span className="text-text-secondary">Range:</span>
            <span className="ml-1 font-medium">
              {vehicle.specifications.range} mi
            </span>
          </div>
          <div>
            <span className="text-text-secondary">Power:</span>
            <span className="ml-1 font-medium">
              {vehicle.specifications.motorPower} hp
            </span>
          </div>
          <div>
            <span className="text-text-secondary">0-60:</span>
            <span className="ml-1 font-medium">
              {vehicle.specifications.acceleration}s
            </span>
          </div>
          <div>
            <span className="text-text-secondary">Type:</span>
            <span className="ml-1 font-medium capitalize">
              {vehicle.specifications.bodyType}
            </span>
          </div>
        </div>

        {/* Price and Mileage (for marketplace) */}
        {showPrice && (
          <div className="flex items-center justify-between mb-3">
            {vehicle.price && (
              <div className="text-lg font-bold text-primary">
                {formatPrice(vehicle.price)}
              </div>
            )}
            {vehicle.mileage && (
              <div className="text-sm text-text-secondary">
                {formatMileage(vehicle.mileage)}
              </div>
            )}
          </div>
        )}

        {/* Location */}
        {vehicle.location && (
          <div className="flex items-center text-sm text-text-secondary mb-3">
            <span className="mr-1">📍</span>
            {vehicle.location}
          </div>
        )}

        {/* Condition */}
        {vehicle.condition && (
          <div className="mb-3">
            <Badge
              variant={
                vehicle.condition === "new"
                  ? "success"
                  : vehicle.condition === "excellent"
                  ? "primary"
                  : vehicle.condition === "good"
                  ? "info"
                  : "warning"
              }
              size="sm"
            >
              {vehicle.condition.charAt(0).toUpperCase() +
                vehicle.condition.slice(1)}
            </Badge>
          </div>
        )}

        {/* Description */}
        {vehicle.description && (
          <p className="text-sm text-text-secondary line-clamp-2 mb-3">
            {vehicle.description}
          </p>
        )}

        {/* Actions */}
        {showActions && (
          <div className="flex gap-2">
            <Button size="sm" className="flex-1">
              View Details
            </Button>
            {vehicle.isForSale && (
              <Button variant="outline" size="sm">
                Contact Seller
              </Button>
            )}
          </div>
        )}
      </div>
    </Card>
  );

  if (href) {
    return (
      <Link href={href} className="block">
        {cardContent}
      </Link>
    );
  }

  return cardContent;
};

export default VehicleCard;
